package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.projectmanagement.api.dto.request.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.FinanceResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 项目立项
 */
public interface IProApprovalApi {
    /**
     * 路径
     */
    String PATH = "/api/project/approval/by";

    /**
     * 投标总结立项API
     * @param request
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/tender")
    @ApiOperation(value = "投标总结立项API", notes = "市场营销投标总结立项接口")
    GenericityResponse<Boolean> approvalByTender(
            @Validated @ApiParam(value = "市场营销入参对象", required = true) @RequestBody MarketProReq<BidSummaryReq> request);

    /**
     * 局内分包合同立项API
     * @param request
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/bureau-contract")
    @ApiOperation(value = "局内分包合同立项API", notes = "市场营销局内分包合同立项接口")
    GenericityResponse<Boolean> approvalByBureauContract(
            @Validated @ApiParam(value = "市场营销入参对象", required = true) @RequestBody MarketProReq<BureauContractReq> request);

    /**
     * 补充协议立项API
     * @param request
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/supplementary-agreement")
    @ApiOperation(value = "补充协议立项API", notes = "市场营销补充协议/补充协议定案立项接口")
    GenericityResponse<Boolean> approvalBySupplementaryAgreement(
            @Validated @ApiParam(value = "市场营销入参对象", required = true) @RequestBody MarketProReq<SupplementaryAgreementReq> request);

    /**
     * 立项数据获取API
     * @param id
     * @param type
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @GetMapping("/getData")
    @ApiOperation(value = "立项数据获取API",notes = "财商系统根据id获取项目数据")
    GenericityResponse<FinanceResp> financeGetData(
            @ApiParam(value = "推送数据id", required = true) @RequestParam("id") String id,
            @ApiParam(value = "推送数据类型", required = true) @RequestParam("type") String type);

    /**
     * 立项回调API
     * @param financeReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/updateData")
    @ApiOperation(value = "立项回调API",notes = "财商系统立项完成后回调项目中心")
    GenericityResponse<Boolean> financeUpdateData(
            @Validated @ApiParam(value = "财商立项回调参数", required = true) @RequestBody FinanceReq financeReq);

    /**
     * 重置非独立性挂接文件
     * @param resetReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PutMapping("/reset/non-independent-project")
    @ApiOperation(value = "重置非独立性挂接文件")
    GenericityResponse<Boolean> resetNonIndependentProject(
        @Validated @ApiParam(value = "非独立挂接文件相关信息", required = true) @RequestBody NonIndependentProjectReq resetReq);


}
