package com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 市场营销文件录入共用对象
 *
 * <AUTHOR>
 * @date 2023/12/09
 */
@Data
@ApiModel(value = "MarketFileBaseReq", description = "市场营销文件录入共用对象")
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = As.EXTERNAL_PROPERTY,
        visible = true,
        property = "belongFileType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = BidSummaryFileReq.class, name = "tender_summary"),
        @JsonSubTypes.Type(value = BidContractEntryFileReq.class, name = "presentation"),
        @JsonSubTypes.Type(value = BidSupplementaryAgreementFileReq.class, name = "agreement_presentation"),
        @JsonSubTypes.Type(value = BidSupplementaryAgreementFileReq.class, name = "agreement"),
        @JsonSubTypes.Type(value = BidSupplementaryAgreementFileReq.class, name = "no_contract_agreement"),
        @JsonSubTypes.Type(value = BidBureauContractFileReq.class, name = "internal_presentation"),
        @JsonSubTypes.Type(value = BidBureauSupplementaryAgreementFileReq.class, name = "internal_agreement"),
        @JsonSubTypes.Type(value = BidBureauSupplementaryAgreementFileReq.class, name =
                "no_contract_internal_agreement"),
//        @JsonSubTypes.Type(value = InvestmentFileOpenReq.class, name = "investment")
})
public abstract class MarketFileBaseReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    @JsonProperty("belongFileType")
    private String belongFileType;

    /**
     * 发起人
     */
    @ApiModelProperty(value = "发起人", position = 1)
    @Size(max = 50, message = "发起人不超过50个字符")
    protected String submitPerson;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称", position = 9)
    @Size(max = 128, message = "工程名称的长度必须小于128")
    @NotNull(message = "工程名称不能为空")
    protected String projectName;

    /**
     * 项目归属
     */
    @ApiModelProperty(value = "项目归属", position = 6)
    @Size(min = 1, max = 128, message = "项目归属长度要求[{min},{max}]")
    protected String projectBelong;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 33)
    @Size(min = 1, max = 255, message = "客户名称长度要求[{min},{max}]")
    protected String customerName;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 8)
    @Size(max = 255, message = "客户母公司的长度必须小于{max}}")
    protected String superiorCompanyName;

    /**
     * 企业类型
     */
    @ApiModelProperty(value = "企业类型", position = 35)
    @Size(min = 1, max = 255, message = "企业类型长度要求[{min},{max}]")
    protected String enterpriseType;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人", position = 36)
    @Size(max = 255, message = "联系人不超过{max}个字符")
    protected String contactPerson;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话", position = 37)
    @Size(max = 64, message = "联系人电话不超过{max}个字符")
    protected String contactPersonMobile;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式", position = 82)
    @Size(max = 64, message = "计价方式不超过{max}个字符")
    protected String pricingMethod;

    /**
     * 合同形式
     */
    @ApiModelProperty(value = "合同形式", position = 83)
    @Size(max = 64, message = "合同形式不超过{max}个字符")
    protected String contractForm;

    /**
     * 项目类型
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)", position = 12)
    @Size(min = 1, max = 32, message = "工程类型(总公司综合口径)长度要求[{min},{max}]")
    protected String projectType;

    /**
     * 另一种项目类型标识
     */
    @ApiModelProperty(value = "工程类型（总公司综合口径）2", position = 16)
    @Size(max = 32, message = "工程类型（总公司综合口径）2不超过{max}个字符")
    protected String projectType2;

    /**
     * 合同总金额
     */
    @ApiModelProperty(value = "合同总金额", position = 44)
    protected BigDecimal totalAmount;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额", position = 45)
    protected BigDecimal noTaxIncludedMoney;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", position = 7)
    @Size(max = 50, message = "省名称长度不超过50个字符")
    protected String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", position = 8)
    @Size(max = 50, message = "市名称长度不超过50个字符")
    protected String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区", position = 9)
    @Size(max = 50, message = "区名称长度不超过50个字符")
    protected String region;

    /**
     * 具体地址
     */
    @ApiModelProperty(value = "具体地址", position = 10)
    @Size(max = 255, message = "具体地址的长度必须小于{max}")
    protected String address;

    /**
     * 国别
     */
    @ApiModelProperty(value = "国别", position = 11)
    @Size(max = 128, message = "国名称长度不超过{max}个字符")
    protected String country;


    /**
     * 云枢组织名称
     */
    @ApiModelProperty(value = "云枢组织名称", position = 104)
    private String yunshuExecuteUnit;

    /**
     * 云枢组织ID
     */
    @ApiModelProperty(value = "云枢组织ID", position = 104)
    private String yunshuExecuteUnitId;

    /**
     * 云枢组织编码
     */
    @ApiModelProperty(value = "云枢组织编码", position = 104)
    private String yunshuExecuteUnitCode;

    /**
     * 云枢组织idPath
     */
    @ApiModelProperty(value = "云枢组织idPath", position = 104)
    private String yunshuExecuteUnitIdPath;


    /**
     * 工程类型Code（国家标准）
     */
    @ApiModelProperty(value = "工程类型Code（国家标准）")
    private String countryProjectTypeCode;
    /**
     * 工程类型code（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型code（总公司市场口径）")
    private String marketProjectTypeCode;
    /**
     * 工程类型code（总公司市场口径）2
     */
    @ApiModelProperty(value = "工程类型code（总公司市场口径）2")
    private String marketProjectType2Code;
    /**
     * 工程类型code(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)")
    private String projectTypeCode;
    /**
     * 工程类型code(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)2")
    private String projectType2Code;
    /**
     * 工程类型code(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)3")
    private String projectType3Code;
    /**
     * 工程类型code(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型code(总公司综合口径)4")
    private String projectType4Code;
    /**
     * 工程承包模式Code
     */
    @ApiModelProperty(value = "工程承包模式Code")
    private String contractMode1Code;

    /**
     * 工程承包模式Code
     */
    @ApiModelProperty(value = "施工承包模式Code")
    private String contractMode2Code;

    /**
     * 投资主体Code
     */
    @ApiModelProperty(value = "投资主体Code")
    private String investorsCode;
    /**
     * 业务类型Code
     */
    @ApiModelProperty(value = "业务类型Code")
    private String businessTypeCode;

    /**
     * 客户级别Code
     */
    @ApiModelProperty(value = "客户级别Code")
    private String customerLevelCode;
    /**
     * 客户企业性质Code
     */
    @ApiModelProperty(value = "客户企业性质Code")
    private String enterpriseTypeCode;

    /**
     * 合同进度款付款方式Code
     */
    @ApiModelProperty(value = "合同进度款付款方式Code")
    private String advancesWayCode;

    /**
     * 合同质量奖罚类型Code
     */
    @ApiModelProperty(value = "合同质量奖罚类型Code", position = 78)
    private String rewardPunishTypeCode;

    /**
     * 局标准分类Code1
     */
    @ApiModelProperty(value = "局标准分类Code1")
    private String standardType1Code;

    /**
     * 局标准分类Code2
     */
    @ApiModelProperty(value = "局标准分类Code2")
    private String standardType2Code;

    /**
     * 局标准分类Code3
     */
    @ApiModelProperty(value = "局标准分类Code3")
    private String standardType3Code;

    /**
     * 局标准分类Code4
     */
    @ApiModelProperty(value = "局标准分类Code4")
    private String standardType4Code;

    /**
     * 创新业务分类Code
     */
    @ApiModelProperty(value = "创新业务分类Code")
    private String innovativeBusinessTypeCode;
    /**
     * 创新业务分类2Code
     */
    @ApiModelProperty(value = "创新业务分类2Code")
    private String innovativeBusinessType2Code;

    /**
     * "创新业务分类3Code"
     */
    @ApiModelProperty(value = "创新业务分类3Code")
    private String innovativeBusinessType3Code;

    @ApiModelProperty(value = "战新业务一级分类code")
    private String strategicNewBusinessTypeCode;

    @ApiModelProperty(value = "战新业务二级分类code")
    private String strategicNewBusinessType2Code;

    @ApiModelProperty(value = "战新业务三级分类code")
    private String strategicNewBusinessType3Code;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户ID ")
    private String customerId;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户母公司id
     */
    @ApiModelProperty(value = "客户母公司id")
    private String superiorCompanyId;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String businessLicenseCode;
}
