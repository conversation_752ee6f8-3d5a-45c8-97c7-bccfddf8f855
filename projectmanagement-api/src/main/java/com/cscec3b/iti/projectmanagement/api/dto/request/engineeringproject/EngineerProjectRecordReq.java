package com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "工程项目信息补录请求参数")
public class EngineerProjectRecordReq implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 工程项目id
     */
    @ApiModelProperty(value = "工程项目id")
    @NotNull(message = "工程项目id不能为空")
    private Long engineerProjectId;

    /**
     * 部门映射标准项目 ID
     */
    @ApiModelProperty(value = "项目部关联的标准项目 ID")
    @NotNull(message = "项目部关联的标准项目 ID 不能为空")
    private Long deptMappingStandardProjectId;

    /**
     * 同步设置为主施工项目
     */
    @ApiModelProperty(value = "同步设置为主施工项目")
    private Boolean syncAsMainStandardProject;
}
