package com.cscec3b.iti.projectmanagement.api.dto.request.open;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("通过云枢批量查询项目列表请求参数")
public class OpenProjectPageByYunshuIdsReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @Size(min = 1, max = 100, message = "仅支持1-100条信息,请检查查询数量")
    @ApiModelProperty(value = "云枢id集合")
    private Set<String> yunshuIds;

}
