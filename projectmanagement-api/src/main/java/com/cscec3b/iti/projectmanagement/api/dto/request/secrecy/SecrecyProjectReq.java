package com.cscec3b.iti.projectmanagement.api.dto.request.secrecy;

import com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;


/**
 * 新建保密请求体
 * <AUTHOR>
 * @date 2024/04/11
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CreateSecrecyProjectReq", description = "新建保密请求体")
public class SecrecyProjectReq extends SecrecyProjectDto {
    /**
     * 行政区域（地理位置）
     */
    @ApiModelProperty(value = "行政区域（地理位置）", position = 29)
    private String region;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 38)
    @Size(max = 255, message = "客户母公司的字段长度必须小于[{max}]")
    private String superiorCompanyName;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 40)
    @Size(max = 255, message = "建设单位（甲方）联系人的字段长度必须小于[{max}]")
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话", position = 41)
    @Size(max = 64, message = "建设单位（甲方）联系人电话的字段长度必须小于[{max}]")
    private String contactPersonMobile;

    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 42)
    @Size(max = 255, message = "设计单位的字段长度必须小于[{max}]")
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 43)
    @Size(max = 255, message = "监理单位的字段长度必须小于[{max}]")
    private String supervisor;

    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 44)
    private Long successfulTime;

    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 45)
    private Long actualSignedTime;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 46)
    @Size(max = 255, message = "签约主体的字段长度必须小于[{max}]")
    private String signedSubjectValue;

    /**
     * 签约主体Code
     */
    @ApiModelProperty(value = "签约主体Code", position = 46)
    @Size(max = 128, message = "签约主体Code的字段长度必须小于[{max}]")
    private String signedSubjectCode;

    /**
     * 项目级别
     */
    @ApiModelProperty(value = "项目级别", position = 12)
    private String projectLevel;

    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 47)
    @Size(max = 255, message = "实施单位的字段长度必须小于[{max}]")
    private String doUnit;

    /**
     * 含税合同总价（人民币）
     */
    @ApiModelProperty(value = "含税合同总价（人民币）", position = 48)
    @DecimalMin(value = "0.00", message = "含税合同总价（人民币）格式不正确")
    private BigDecimal totalAmount;

    /**
     * 合同总价（不含税）
     */
    @ApiModelProperty(value = "合同总价（不含税）", position = 49)
    @DecimalMin(value = "0.00", message = "合同总价（不含税）格式不正确")
    private BigDecimal noTaxIncludedMoney;

    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额", position = 50)
    @DecimalMin(value = "0.00", message = "自行施工不含税金额格式不正确")
    private BigDecimal midAmountSelf;

    /**
     * 土建不含税金额
     */
    @ApiModelProperty(value = "土建不含税金额", position = 51)
    @DecimalMin(value = "0.00", message = "土建不含税金额格式不正确")
    private BigDecimal selfCivilAmount;

    /**
     * 安装不含税金额
     */
    @ApiModelProperty(value = "安装不含税金额", position = 52)
    @DecimalMin(value = "0.00", message = "安装不含税金额格式不正确")
    private BigDecimal selfInstallAmount;

    /**
     * 钢结构不含税金额
     */
    @ApiModelProperty(value = "钢结构不含税金额", position = 53)
    @DecimalMin(value = "0.00", message = "钢结构不含税金额格式不正确")
    private BigDecimal selfSteelStructureAmount;

    /**
     * 总包服务费
     */
    @ApiModelProperty(value = "总包服务费", position = 54)
    @DecimalMin(value = "0.00", message = "总包服务费金额格式不正确")
    private BigDecimal selfTotalServiceAmount;

    /**
     * 其他费用
     */
    @ApiModelProperty(value = "其他费用", position = 55)
    @DecimalMin(value = "0.00", message = "其他费用金额格式不正确")
    private BigDecimal selfOtherAmount;

    /**
     * 暂列金额或甲指分包金额
     */
    @ApiModelProperty(value = "暂列金额或甲指分包金额", position = 56)
    @DecimalMin(value = "0.00", message = "暂列金额或甲指分包金额格式不正确")
    private BigDecimal subcontractAmount;

    /**
     * 销项税额
     */
    @ApiModelProperty(value = "销项税额", position = 57)
    @DecimalMin(value = "0.00", message = "销项税额格式不正确")
    private BigDecimal projectTaxAmount;

    /**
     * 合同项目经理
     */
    @ApiModelProperty(value = "合同项目经理", position = 66)
    @Size(max = 128, message = "合同项目经理的字段长度必须小于[{max}]")
    private String contractManager;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 73)
    @Size(max = 2048, message = "发包人指定分包、独立分包的工程的字段长度必须小于[{max}]")
    private String issuerProject;

    /**
     * 合同承包范围
     */
    @ApiModelProperty(value = "合同承包范围", position = 63)
    private String contractScope;

    /**
     * 总工期（天）
     */
    @ApiModelProperty(value = "总工期（天）", position = 74)
    private Integer countDays;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期", position = 75)
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期", position = 76)
    private Long workerEndTime;

    @ApiModelProperty(value = "实际进场日期", position = 11)
    private Long realEnterTime;

    @ApiModelProperty(value = "实际竣工日期", position = 12)
    private Long workEndTime;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 62)
    @Size(max = 512, message = "工期奖罚条款的字段长度必须小于[{max}]")
    private String workerRewardPunishAppoint;

    /**
     * 工程参数json
     */
    @ApiModelProperty(value = "工程参数json", position = 21)
    private String engineerParameter;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期", position = 77)
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 78)
    private Long predictWorkEndTime;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "项目附件信息对象", position = 130)
    @Valid
    @Size(min = 0, max = 10, message = "附件数量不能超过10个")
    private List<AttachmentDto> attachments;

}
