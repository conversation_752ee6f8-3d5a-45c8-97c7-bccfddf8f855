package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel(value = "ContractualRelationshipResp", description = "合同/补充协议关系信息响应对象")
public class ContractualRelationshipResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "根节点名称")
    private String rootContractName;

    @ApiModelProperty(value = "根节点合同在项目中心表中主键")
    private Long rootContractId;

    @ApiModelProperty(value = "根节点合同类型")
    private Integer rootContractType;

    @ApiModelProperty(value = "叶子节点数据")
    private List<ContractLeafResp> rootContractList;

}
