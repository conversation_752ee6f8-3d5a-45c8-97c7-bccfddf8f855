package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 通过合同信息查询项目信息请求体
 *
 * <AUTHOR>
 * @date 2023/03/31 15:09
 **/

@Data
@Accessors(chain = true)
@ApiModel(value = "项目详情信息(通过合同查询)", description = "项目详情信息(通过合同查询)")
public class ProjectOpenByFileResp implements Serializable {

    /**
     * 所属组织编码
     */
    @ApiModelProperty(value = "所属组织编码")
    private String projectDeptId;

    /**
     * 所属指挥部/项目部
     */
    @ApiModelProperty(value = "所属指挥部/项目部")
    private String projectDeptName;

    /**
     * 项目编码
     */
    @ApiModelProperty(value = "项目编码")
    private String projectFinanceCode;

    /**
     * 云枢组织id
     */
    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    /**
     * A8项目编码
     */
    @ApiModelProperty(value = "A8项目编码")
    private String a8ProjectCode;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectFinanceName;

    /**
     * 项目简称（中文）
     */
    @ApiModelProperty(value = "项目简称（中文）")
    private String projectFinanceAbbreviation;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）")
    private String marketProjectType;

    /**
     * 工程类型（总公司综合口径）
     */
    @ApiModelProperty(value = "工程类型（总公司综合口径）")
    private String projectType;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式")
    private String contractMode;

    /**
     * 行政区域（地理位置）
     */
    @ApiModelProperty(value = "行政区域（地理位置）")
    private String region;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    /**
     * 是否投资项目
     */
    @ApiModelProperty(value = "是否投资项目")
    private String investmentProjects;
    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体")
    private String investors;
    /**
     * 是否生态敏感区项目
     */
    @ApiModelProperty(value = "是否生态敏感区项目")
    private Boolean isEcologySensitive;
    /**
     * 是否边小远散项目
     */
    @ApiModelProperty(value = "是否边小远散项目")
    private Boolean isEdgeSmall;

    /**
     * 项目级别
     */
    @ApiModelProperty(value = "项目级别")
    private String projectLevel;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人")
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话")
    private String contactPersonMobile;

    /**
     * 现场业主代表职务
     */
    @ApiModelProperty(value = "现场业主代表职务")
    private String sceneOwnerRepresentDuty;
    /**
     * 现场业主代表姓名
     */
    @ApiModelProperty(value = "现场业主代表姓名")
    private String sceneOwnerRepresentName;
    /**
     * 现场业主代表联系电话
     */
    @ApiModelProperty(value = "现场业主代表联系电话")
    private String sceneOwnerRepresentPhone;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期")
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期")
    private Long predictWorkEndTime;

    /**
     * 实际进场日期
     */
    @ApiModelProperty(value = "实际进场日期")
    private Long realEnterTime;

    /**
     * 实际竣工日期
     */
    @ApiModelProperty(value = "实际竣工日期")
    private Long workEndTime;

    /**
     * 五方主体验收日期（实际通车时间）
     */
    @ApiModelProperty(value = "五方主体验收日期（实际通车时间）")
    private Long realOpenTrafficTime;

    /**
     * 施工项目状态(商务): 05:未结; 06:已结
     */
    @ApiModelProperty(value = "施工项目状态(商务): 05:未结; 06:已结")
    private String projectStatusBiz;
    /**
     * 施工项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;
     */
    @ApiModelProperty(value = "施工项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;")
    private String projectStatusEng;
    /**
     * 施工项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;
     */
    @ApiModelProperty(value = "施工项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;")
    private String projectStatusFin;

    /**
     * 合同id(入参合同id)
     */
    @ApiModelProperty(value = "透传字段：合同id(入参合同id)")
    private String contractId;

    /**
     * 合同id(入参合同id)
     */
    @ApiModelProperty(value = "透传字段：合同类型(入参合同类型)")
    private Integer contractType;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称,财商立项完成前为工程名称，财商立项完成后为财商项目名称")
    private String cpmProjectName;
}
