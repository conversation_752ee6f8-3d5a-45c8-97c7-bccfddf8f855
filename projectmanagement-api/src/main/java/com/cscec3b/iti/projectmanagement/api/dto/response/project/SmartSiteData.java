
package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 智慧工地响应数据
 * @date 2022/11/16
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SmartSiteResp", description = "智慧工地响应数据")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SmartSiteData implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目id")
    private String projectId;

    @ApiModelProperty(value = "A8项目编码(对应智慧工地产值编号字段)")
    @JsonProperty("outputValueNo")
    private String a8ProjectCode;

    @ApiModelProperty(value = "项目部名称")
    @JsonProperty("projectTitle")
    private String title;

    @ApiModelProperty(value = "项目部简称")
    private String projectAbbreviation;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区")
    private String district;

    @ApiModelProperty(value = "地址")
    private String address;

    @ApiModelProperty(value = "坐标x")
    private BigDecimal pointx;

    @ApiModelProperty(value = "坐标y")
    private BigDecimal pointy;

    @ApiModelProperty(value = "合同备案项目经理id")
    private String projectManagerMid;

    @ApiModelProperty(value = "合同备案项目经理姓名")
    private String projectManagerName;

    @ApiModelProperty(value = "合同备案项目经理电话")
    private String projectManagerPhone;

    @ApiModelProperty(value = "项目执行经理id")
    private String projectExecutiveManagerMid;

    @ApiModelProperty(value = "项目执行经理姓名")
    private String projectExecutiveManagerName;

    @ApiModelProperty(value = "项目执行经理电话")
    private String projectExecutiveManagerPhone;

    @ApiModelProperty(value = "安监责任人id")
    private String safetySupervisionMid;

    @ApiModelProperty(value = "安监责任人姓名")
    private String safetySupervisionName;

    @ApiModelProperty(value = "安监责任人电话")
    private String safetySupervisionPhone;

    @ApiModelProperty("开工日期")
    private String startDate;

    @ApiModelProperty("竣工日期")
    private String completionDate;

    @ApiModelProperty("合同日历天数")
    private int days;

    @ApiModelProperty("下令开工时间")
    private String orderStart;

    @ApiModelProperty("实际开工时间")
    private String actualStart;

    @ApiModelProperty("竣工备案日期")
    private String completionRecordTime;

    @ApiModelProperty("计划交付日期")
    private String plannedDeliveryTime;

    @ApiModelProperty("验收时间")
    private String acceptance;

    @ApiModelProperty("实际通车时间")
    private String openTime;

    @ApiModelProperty("云枢组织id")
    private String yunshuOrgId;

    @ApiModelProperty("筹备/立项/施工准备/在建/停工/完工待验/竣工/维保/销项")
    private String projectStatus;
}