package com.cscec3b.iti.projectmanagement.api.dto.response.special;

import com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description SpecialProjectDetailResp
 * <AUTHOR>
 * @Date 2023/2/14 9:49
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SpecialProjectDetailResp", description = "特殊立项详情响应对象")
public class SpecialProjectDetailResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 执行单位名称
     */
    @ApiModelProperty(value = "执行单位")
    private String executeUnit;

    /**
     * 执行单位简称
     */
    @ApiModelProperty(value = "执行单位简称")
    private String executeUnitAbbreviation;

    /**
     * 来源系统
     */
    @ApiModelProperty(value = "来源系统，1:市场营销；2:特殊立项")
    private Integer sourceSystem;

    /**
     * 立项描述
     */
    @ApiModelProperty(value = "立项描述")
    private String projectDesc;

    /**
     * 特殊立项项目分类id
     */
    @ApiModelProperty(value = "特殊立项项目分类id")
    private String projectClassId;

    /**
     * 行政区域的idpath
     */
    @ApiModelProperty(value = "行政区域的idpath")
    private String regionIdPath;

    /**
     * 项目分类名称
     */
    @ApiModelProperty(value = "项目分类名称")
    private String projectClassName;

    /**
     * 项目分类idpath
     */
    @ApiModelProperty(value = "项目分类idpath")
    private String projectClassIdPath;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址", position = 15)
    @Size(max = 128, message = "项目地址的字段长度必须小于[{max}]")
    private String projectAddress;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 36)
    private String investors;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 37)
    private String customerName;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 38)
    private String superiorCompanyName;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 40)
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话", position = 41)
    private String contactPersonMobile;

    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 42)
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 43)
    private String supervisor;

    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 44)
    private Long successfulTime;

    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 45)
    private Long actualSignedTime;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 46)
    private String signedSubjectValue;

    /**
     * 签约主体code
     */
    @ApiModelProperty(value = "签约主体code", position = 47)
    private String signedSubjectCode;


    /**
     * 项目级别
     */
    @ApiModelProperty(value = "项目级别", position = 12)
    private String projectLevel;

    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 47)
    private String doUnit;

    /**
     * 合同总金额（元）
     */
    @ApiModelProperty(value = "合同总金额（元）", position = 34)
    private BigDecimal contractAmount;

    /**
     * 含税合同总价（人民币）
     */
    @ApiModelProperty(value = "含税合同总价（人民币）", position = 48)
    private BigDecimal totalAmount;

    /**
     * 合同总价（不含税）
     */
    @ApiModelProperty(value = "合同总价（不含税）", position = 49)
    private BigDecimal noTaxIncludedMoney;

    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额", position = 50)
    private BigDecimal midAmountSelf;

    /**
     * 土建不含税金额
     */
    @ApiModelProperty(value = "土建不含税金额", position = 51)
    private BigDecimal selfCivilAmount;

    /**
     * 安装不含税金额
     */
    @ApiModelProperty(value = "安装不含税金额", position = 52)
    private BigDecimal selfInstallAmount;

    /**
     * 钢结构不含税金额
     */
    @ApiModelProperty(value = "钢结构不含税金额", position = 53)
    private BigDecimal selfSteelStructureAmount;

    /**
     * 总包服务费
     */
    @ApiModelProperty(value = "总包服务费", position = 54)
    private BigDecimal selfTotalServiceAmount;

    /**
     * 其他费用
     */
    @ApiModelProperty(value = "其他费用", position = 55)
    private BigDecimal selfOtherAmount;

    /**
     * 暂列金额或甲指分包金额
     */
    @ApiModelProperty(value = "暂列金额或甲指分包金额", position = 56)
    private BigDecimal subcontractAmount;

    /**
     * 销项税额
     */
    @ApiModelProperty(value = "销项税额", position = 57)
    private BigDecimal projectTaxAmount;

    /**
     * 合同项目经理
     */
    @ApiModelProperty(value = "合同项目经理", position = 66)
    private String contractManager;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 73)
    private String issuerProject;

    /**
     * 合同承包范围
     */
    @ApiModelProperty(value = "合同承包范围", position = 63)
    private String contractScope;

    /**
     * 总工期（天）
     */
    @ApiModelProperty(value = "总工期（天）", position = 74)
    private Integer countDays;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期", position = 75)
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期", position = 76)
    private Long workerEndTime;

    @ApiModelProperty(value = "实际进场日期", position = 11)
    private Long realEnterTime;

    @ApiModelProperty(value = "实际竣工日期", position = 12)
    private Long workEndTime;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 62)
    private String workerRewardPunishAppoint;


    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期", position = 77)
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 78)
    private Long predictWorkEndTime;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "项目附件信息对象", position = 130)
    @Valid
    private List<AttachmentDto> attachments;

    /**
     * 执行单位id
     */
    @ApiModelProperty(value = "执行单位id", position = 134)
    private String executeUnitId;

    /**
     * 执行单位Code
     */
    @ApiModelProperty(value = "执行单位Code", position = 135)
    private String executeUnitCode;

    /**
     * 执行单位在标准组织的idPath
     */
    @ApiModelProperty(value = "执行单位在标准组织的idPath", position = 136)
    private String executeUnitIdPath;

    /**
     * 财商立项编号
     */
    @ApiModelProperty(value = "财商立项编号")
    private String projectFinanceCode;

    /**
     * 财商立项名称
     */
    @ApiModelProperty(value = "财商立项名称")
    private String projectFinanceName;

    /**
     * 财商立项项目简称（中文）
     */
    @ApiModelProperty(value = "财商立项项目简称（中文）")
    private String projectFinanceAbbreviation;

    /**
     * A8编码
     */
    @ApiModelProperty(value = "A8编码")
    private String a8ProjectCode;

    /**
     * 工程参数json
     */
    @ApiModelProperty(value = "工程参数json")
    private String engineerParameter;

    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    /**
     * 施工项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;
     */
    @ApiModelProperty(value = "施工项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;")
    private String projectStatusEng;

    /**
     * 施工项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;
     */
    @ApiModelProperty(value = "施工项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;")
    private String projectStatusFin;

    /**
     * 施工项目状态(商务): 05:未结; 06:已结
     */
    @ApiModelProperty(value = "施工项目状态(商务): 05:未结; 06:已结")
    private String projectStatusBiz;

    @ApiModelProperty(value = "项目唯一标识 含义：接收市场营销立项通知或特殊立项发起后生成.P+年月日+四位流水号")
    private String cpmProjectKey;
    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    @ApiModelProperty(value = "局名义项目类型")
    private Integer bureauNominalProjectType;

    @ApiModelProperty(value = "总包项目id")
    private Long generalContractProjectId;

    @ApiModelProperty(value = "云枢执行单位名称")
    private String yunshuExecuteUnit;

    @ApiModelProperty(value = "云枢执行单位编码")
    private String yunshuExecuteUnitCode;

    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    @ApiModelProperty(value = "云枢执行单位简称")
    private String yunshuExecuteUnitAbbreviation;

    @ApiModelProperty(value = "云筑网（集采）项目编码")
    private String yzwProjectId;

    @ApiModelProperty(value = "局标准分类")
    private String standardType;

    /**
     * 局标准分类编码路径
     */
    @ApiModelProperty(value = "局标准分类编码路径")
    private String standardTypeCodePath;

    /**
     * 财商业务板块全路径
     */
    @ApiModelProperty(value = "财商业务板块全路径")
    private String financialBusinessSegment;

    /**
     * 财商业务板块codePath
     */
    @ApiModelProperty(value = "财商业务板块codePath")
    private String businessSegmentCodePath;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String lng;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String lat;

    /**
     * 项目简称: 财商未立项时取工程简称，财商立项后为财商简称
     */
    @ApiModelProperty(value = "项目简称: 财商未立项时取工程简称，财商立项后为财商简称")
    private String cpmProjectAbbreviation;

    /**
     * 智慧工地项目云枢组织TreeID
     */
    @ApiModelProperty(value = "智慧工地项目云枢组织TreeID")
    private String yunshuTreeId;

    /**
     * 智慧工地项目云枢组织queryCode
     */
    @ApiModelProperty(value = "智慧工地项目云枢组织queryCode")
    private String yunshuQueryCode;

    /**
     * 智慧工地项目直接上级云枢组织ID
     */
    @ApiModelProperty(value = "智慧工地项目直接上级云枢组织ID")
    private String yunshuParentOrgId;

    /**
     * 智慧工地项目直接上级全称
     */
    @ApiModelProperty(value = "智慧工地项目直接上级全称")
    private String yunshuParentOrgName;

    /**
     * 智慧工地项目直接上级treeID
     */
    @ApiModelProperty(value = "智慧工地项目直接上级treeID")
    private String yunshuParentTreeId;

    /**
     * 项目效果图
     */
    @ApiModelProperty(value = "项目效果图")
    private String effectPic;

    /**
     * 市场业务板块
     */
    @ApiModelProperty(value = "市场业务板块")
    private String marketingBusinessSegment;

    /**
     * 市场业务板块codePath
     */
    @ApiModelProperty(value = "市场业务板块codePath")
    private String marketingBusinessSegmentCodePath;

    /**
     * 财商业务板块
     */
    @ApiModelProperty(value = "财商业务板块")
    private String financeBusinessSegment;

    /**
     * 财商业务板块codepath
     */
    @ApiModelProperty(value = "财商业务板块codepath")
    private String financeBusinessSegmentCodePath;

    /**
     * CPM 业务板块代码路径
     */
    @ApiModelProperty(value = "项目中心业务板块", notes = "项目中心业务板块,财商立项前取市场数据，立项后取财商数据")
    private String cpmBusinessSegmentCodePath;

    /**
     * CPM 业务部门
     */
    @ApiModelProperty(value = "项目中心业务板块名称", notes = "项目中心业务板块,财商立项前取市场数据，立项后取财商数据")
    private String cpmBusinessSegment;


}
