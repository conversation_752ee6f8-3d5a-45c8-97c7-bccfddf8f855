package com.cscec3b.iti.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 施工项目信息响应
 *
 * <AUTHOR>
 * @date 2025/06/05
 */
@Data
@ApiModel(value = "StandardProjectInfoResp", description = "施工项目信息响应")
public class StandardProjectInfoResp implements Serializable {

    @ApiModelProperty(value = "项目编号")
    private Long id;

    /**
     * CPM 项目密钥
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * CPM 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String cpmProjectName;

//    /**
//     * CPM 项目简称
//     */
//    @ApiModelProperty(value = "项目简称")
//    private String cpmProjectAbbreviation;

    /**
     * 财商项目编码
     */
    @ApiModelProperty(value = "财商项目编码")
    private String projectFinanceCode;

    /**
     * 是否主施工项目
     */
    @ApiModelProperty(value = "是否主施工项目")
    private Integer isMain;

}
