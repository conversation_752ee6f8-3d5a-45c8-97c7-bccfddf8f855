package com.cscec3b.iti.cpm.utils;

import com.cscec3b.iti.cpm.config.CpmCallBackProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.util.DigestUtils;

import java.time.Instant;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 神禹服务相关
 *
 * <AUTHOR>
 * @date 2024/06/11
 */
@Slf4j
public class ShenYuUtil {

    private static final String APP_KEY = "appKey";

    private static final String VERSION = "version";
    private static final String PATH = "path";

    private static final String SIGN = "sign";
    private static final String TIME_STAMP = "timestamp";


    public ShenYuUtil() {
        throw new UnsupportedOperationException("error");
    }


    /**
     * 生成httpheaders
     *
     * @return {@link HttpHeaders }
     * <AUTHOR>
     * @date 2023/09/14
     */
    public static HttpHeaders genHttpHeaders(CpmCallBackProperties callBackProperties, String callBackUrl) {
        Map<String, String> authMap = new HashMap<>();
        authMap.put(VERSION, callBackProperties.getVersion());
        authMap.put(TIME_STAMP, String.valueOf(Instant.now().toEpochMilli()));
        authMap.put(PATH, callBackUrl);
        final String sign = genSign(authMap, callBackProperties.getCpmAuthAppSecret());
        authMap.put(APP_KEY, callBackProperties.getCpmAuthAppKey());
        authMap.put(SIGN, sign);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setAll(authMap);
        return httpHeaders;
    }

    /**
     * 生成签名
     *
     * @param map       签名相关参数
     * @param secretKey 密钥
     * @return {@link String }
     * <AUTHOR>
     * @date 2023/09/13
     */

    public static String genSign(Map<String, String> map, String secretKey) {
        final List<String> storedKeys = map.keySet().stream().filter(key -> Objects.nonNull(map.get(key)))
                .sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        final String sign = storedKeys.stream().map(key -> String.join("", key, map.get(key)))
                .collect(Collectors.joining()).trim().concat(secretKey);
        return DigestUtils.md5DigestAsHex(sign.getBytes()).toUpperCase();
    }

}
