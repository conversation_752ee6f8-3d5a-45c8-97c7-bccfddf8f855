package com.cscec3b.iti.projectmanagement.server.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/15 16:30
 */
@Component
@Slf4j
public class SequenceConfig {


    @Resource
    private RedisTemplate<String,Object> redisTemplate;

    //用作存放redis中的key
    private static final String ORDER_KEY = "sw";

    //生成特定的业务编号，prefix为特定的业务代码
    public String getOrderNo (String prefix) {
        return getSeqNo(prefix);
    }

    //SequenceConfig类中公用部分，传入制定的key和prefix
    private String getSeqNo (String prefix){
        Date expireDate =
                Date.from(LocalDateTime.of(LocalDate.now() , LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        //返回当前redis中的key的最大值
        long seq = generate(redisTemplate,ORDER_KEY, expireDate);
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));

        String sequence = StringUtils.leftPad(Long.toString(seq), 3 , "0");
        if (prefix == null) {
            prefix = "";
        }
        //拼接业务编号
        return prefix + date + sequence;
    }

    /**
     * @param key
     * @param expireTime <i>过期时间</i>
     * @return
     */
    public static long generate ( RedisTemplate < ?, ? > redisTemplate , String key , Date expireTime )
    {
        //RedisAtomicLong为原子类，根据传入的key和redis链接工厂创建原子类
        RedisAtomicLong counter = new RedisAtomicLong(key , Objects.requireNonNull(redisTemplate.getConnectionFactory()));
        //设置过期时间
        counter.expireAt(expireTime);
        //返回redis中key的值，内部实现下面详细说明
        return counter.incrementAndGet();
    }
}
