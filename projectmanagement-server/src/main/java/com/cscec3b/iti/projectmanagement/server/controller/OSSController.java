package com.cscec3b.iti.projectmanagement.server.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.projectmanagement.api.IOSSStorageApi;
import com.cscec3b.iti.projectmanagement.api.dto.dto.OSSFileInfo;
import com.cscec3b.iti.projectmanagement.api.dto.response.OSSProcessResp;
import com.cscec3b.iti.projectmanagement.server.service.IOSSProcessService;
import com.cscec3b.iti.projectmanagement.server.service.IStorageService;

import io.swagger.annotations.Api;

/**
 * OSS文件存储
 * <AUTHOR>
 * @date 2022-12-2022/12/30 15:00
 */
@Api(tags = {"OSS文件存储"})
@RestController
@RequestMapping(IOSSStorageApi.PATH)
public class OSSController implements IOSSStorageApi {

    private final IStorageService ossService;

    private final IOSSProcessService ossProcessService;

    public OSSController(IStorageService ossService, IOSSProcessService ossProcessService) {
        this.ossService = ossService;
        this.ossProcessService = ossProcessService;
    }

    /**
     * 文件上传
     * @param file
     * @param businessType
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<OSSFileInfo> upload(MultipartFile file, int businessType) {
        return ResponseBuilder.fromData(ossService.upload(file, businessType));
    }

    /**
     * 异步文件上传
     * @param file
     * @param businessType
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<OSSFileInfo> uploadAsync(MultipartFile file, int businessType) {
        return ResponseBuilder.fromData(ossService.uploadAsync(file, businessType));
    }

    /**
     * 异步文件上传的进度
     * @param fileId
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<OSSProcessResp> uploadAsyncProgress(String fileId) {
        return ResponseBuilder.fromData(ossProcessService.getProcess(fileId));
    }

    /**
     * 删除oss上的文件, 支持批量删除
     * @param objectKeys
     * @return
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> delete(List<String> objectKeys) {
        return ResponseBuilder.fromData(ossService.deleteFile(objectKeys));
    }

}
