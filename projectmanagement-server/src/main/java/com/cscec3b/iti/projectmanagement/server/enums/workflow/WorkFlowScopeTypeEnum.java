package com.cscec3b.iti.projectmanagement.server.enums.workflow;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description: 流程业务类型
 * @date 2023/11/29
 */
@Getter
public enum WorkFlowScopeTypeEnum {

    BUSINESS_SYSTEM_DATA_CHANGE_ENUM("BUSINESS_SYSTEM_CHANGE", "业务系统数据变更"),

    BID_APPROVAL("cpmbid", "营销策划"),

    SECRECY("secrecy", "保密项目信息"),

    CIVIL_MILITARY("civil_military", "军民融合项目信息");



    private final String scopeType;

    private final String desc;

    WorkFlowScopeTypeEnum(String scopeType, String desc) {
        this.scopeType = scopeType;
        this.desc = desc;
    }

    public static List<String> fuzzyToCode(String desc) {
        return Stream.of(WorkFlowScopeTypeEnum.values())
                .filter(s -> s.desc.contains(desc))
                .map(WorkFlowScopeTypeEnum::getScopeType)
                .collect(Collectors.toList());
    }

    public static WorkFlowScopeTypeEnum match(String scopeType) {
        return Arrays.stream(WorkFlowScopeTypeEnum.values()).filter(type -> Objects.equals(scopeType,
                type.getScopeType())).findFirst().orElseThrow(() -> new RuntimeException("未找到对应的枚举类型"));
    }

}
