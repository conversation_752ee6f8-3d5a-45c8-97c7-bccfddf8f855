package com.cscec3b.iti.projectmanagement.server.pushservice.enums;


import lombok.Getter;

/**
 * 手动推送事件流程节点
 *
 * <AUTHOR>
 * @date 2023/11/24
 */
@Getter
public enum ManualPushEventFlowNode {

    /**
     * 市场营销板块
     */
    MARKETING_SEGMENT("市场信息板块", "marketing_segment", "marketingSegment", "create", "市场营销节点"),
    /**
     * 财商板块
     */
    FINANCE_SEGMENT("财务信息板块", "finance_segment", "financeSegment", "create", "财商立项节点"),
    /**
     * 财商板块更新
     */
    FINANCE_SEGMENT_UPDATE("财务信息板块", "finance_segment_update", "financeSegment", "update", "财商立项节点"),

    /**
     * 智慧工地板块
     */
    SMART_SITE_SEGMENT("工程信息版块", "smart_site_segment", "smartSiteSegment", "create", "智慧工地立项节点"),
    /**
     * 智慧工地板块更新
     */
    SMART_SITE_SEGMENT_UPDATE("工程信息版块", "smart_site_segment_update", "smartSiteSegment", "update", "智慧工地立项节点"),

    /**
     * 项目中心信息板块
     */
    CPM_SEGMENT("基本信息板块", "cpm_segment", "cpmSegment", "update", "项目中心基本信息节点"),

    /**
     * 供应链信息更新
     */
    SUPPLY_SEGMENT("供应链信息版块", "supply_segment", "supplySegment", "update", "供应链节点（云筑网编码）"),

    /**
     * 标准立项(财务和工地立项完成)
     */
    FINANCE_SMART_SITE_APPROVAL("标准立项完成", "finance_smart_site_approval", "financeSmartSiteApproval", "create",
            "财商和工地立项完成节点"),

    /**
     * 特殊立项发起
     */
    SPECIAL_PROJECT("特殊立项发起", "special_project", "specialProject", "create", "特殊立项节点"),

    /**
     * 财商立项项目
     */
    FINANCE_PROJECT("财商立项发起", "finance_project", "marketingSegment", "create", "财商立项项目"),

    /**
     * 智慧工地立项项目
     */
    SMART_SITE_PROJECT("智慧工地立项发起", "smart_site_project", "marketingSegment", "create", "智慧工地立项项目");


    final String name;
    final String code;
    final String filed;
    final String type;
    final String description;

    ManualPushEventFlowNode(final String name, final String code, final String filed, String type,
            final String description) {
        this.name = name;
        this.code = code;
        this.filed = filed;
        this.type = type;
        this.description = description;
    }


}
