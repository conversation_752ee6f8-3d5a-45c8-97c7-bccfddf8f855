<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false" packagingData="true">

    <!--预生产环境、测试环境输出简写包名-->
    <springProfile name="prod|pre">
        <property name="OUTPUT_PATTERN"
                  value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{35} | %X{traceId}-%X{TRACE_ID} | %line | %msg%n"/>
    </springProfile>
    <!--本机、开发环境输出完整包名-->
    <springProfile name="local|dev|test">
        <property name="OUTPUT_PATTERN"
                  value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger | %X{traceId}-%X{spanId} | %line | %msg%n"/>
    </springProfile>

    <!--日志文件夹路径，可以在bootstrap.yml里配置-->
    <springProperty scope="context" name="springlogdir" source="log.dir" defaultValue="./logs/"/>

    <!--服务名称-->
    <springProperty scope="context" name="applicationName" source="spring.application.name"/>
    <springProfile name="local">
        <property name="logdir"
                  value="${springlogdir}"/>
    </springProfile>
    <springProfile name="prod|pre|test|dev">
        <property name="logdir"
                  value="/app/logs/${K8S_POD_NAMESPACE}/${applicationName}/${K8S_POD_NAME}/"/>
    </springProfile>


    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${OUTPUT_PATTERN}</pattern>
        </encoder>
    </appender>

    <!--全部日志文件-->
    <appender name="all" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${logdir}${applicationName}-%d{yyyy-MM-dd}-%i.log</FileNamePattern>
            <!--每个日志文件20m-->
            <maxFileSize>20MB</maxFileSize>
            <!--日志文件保留天数-->
            <MaxHistory>7</MaxHistory>>
            <!--最多保存100m-->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${OUTPUT_PATTERN}</pattern>
        </encoder>
    </appender>

    <!--业务包日志-->
    <appender name="cscec3b-iti" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${logdir}cscec3b-iti-${applicationName}-%d{yyyy-MM-dd}-%i.log</FileNamePattern>
            <!--每个日志文件20m-->
            <maxFileSize>20MB</maxFileSize>
            <!--日志文件保留天数-->
            <MaxHistory>7</MaxHistory>>
            <!--最多保存100m-->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${OUTPUT_PATTERN}</pattern>
        </encoder>
    </appender>

    <!--nacos日志-->
    <appender name="alibaba" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${logdir}alibaba-${applicationName}-%d{yyyy-MM-dd}-%i.log</FileNamePattern>
            <!--每个日志文件20m-->
            <maxFileSize>20MB</maxFileSize>
            <!--日志文件保留天数-->
            <MaxHistory>7</MaxHistory>>
            <!--最多保存100m-->
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${OUTPUT_PATTERN}</pattern>
        </encoder>
    </appender>

    <!--生产环境、预生产环境只输出错误，并且分包显示-->
    <springProfile name="prod">
        <root>
            <level value="warning"/>
            <appender-ref ref="console"/>
            <appender-ref ref="all"/>
        </root>
        <logger name="com.cscec3b.iti" level="info" additivity="true">
            <appender-ref ref="cscec3b-iti"/>
        </logger>
        <logger name="com.alibaba" level="error" additivity="false">
            <appender-ref ref="alibaba"/>
        </logger>
        <logger name="Validator" level="INFO"/>
    </springProfile>

    <!--测试环境输出提示信息信息-->
    <springProfile name="test|pre">
        <root>
            <level value="info"/>
            <appender-ref ref="console"/>
            <appender-ref ref="all"/>
        </root>
        <logger name="com.cscec3b.iti" level="info" additivity="true">
            <appender-ref ref="cscec3b-iti"/>
        </logger>
        <logger name="com.alibaba" level="info" additivity="false">
            <appender-ref ref="alibaba"/>
        </logger>
        <logger name="Validator" level="INFO"/>
    </springProfile>
    <!--开发环境输出调试信息-->
    <springProfile name="dev">
        <root level="info">
            <appender-ref ref="console"/>
            <appender-ref ref="all"/>
        </root>
        <logger name="com.cscec3b.iti" level="debug" additivity="true">
            <appender-ref ref="cscec3b-iti"/>
        </logger>

        <logger name="com.alibaba" level="info" additivity="false">
            <appender-ref ref="alibaba"/>
        </logger>
        <logger name="Validator" level="INFO"/>
    </springProfile>
    <!--本机-->
    <springProfile name="local">
        <root level="info">
            <appender-ref ref="console"/>
            <appender-ref ref="all"/>
        </root>
        <logger name="com.cscec3b.iti" level="info" additivity="true">
            <appender-ref ref="cscec3b-iti"/>
        </logger>

        <logger name="com.alibaba" level="error" additivity="true">
            <appender-ref ref="alibaba"/>
        </logger>
        <logger name="Validator" level="INFO"/>
    </springProfile>
</configuration>
