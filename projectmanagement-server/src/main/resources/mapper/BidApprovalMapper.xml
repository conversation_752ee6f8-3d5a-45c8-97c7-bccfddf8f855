<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.BidApproval">
        <!--@mbg.generated-->
        <!--@Table bid_approval-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="belong_id" jdbcType="BIGINT" property="belongId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode"/>
        <result column="presentation_code" jdbcType="VARCHAR" property="presentationCode"/>
        <result column="project_code" jdbcType="VARCHAR" property="projectCode"/>
        <result column="investment_code" jdbcType="VARCHAR" property="investmentCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="project_belong" jdbcType="VARCHAR" property="projectBelong"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="region" jdbcType="VARCHAR" property="region"/>
        <result column="country" jdbcType="VARCHAR" property="country"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="country_project_type" jdbcType="VARCHAR" property="countryProjectType"/>
        <result column="market_project_type" jdbcType="VARCHAR" property="marketProjectType"/>
        <result column="market_project_type2" jdbcType="VARCHAR" property="marketProjectType2"/>
        <result column="project_type" jdbcType="VARCHAR" property="projectType"/>
        <result column="project_type2" jdbcType="VARCHAR" property="projectType2"/>
        <result column="project_type3" jdbcType="VARCHAR" property="projectType3"/>
        <result column="project_type4" jdbcType="VARCHAR" property="projectType4"/>
        <result column="is_create_head" jdbcType="VARCHAR" property="createHead"/>
        <result column="is_independ_project" jdbcType="VARCHAR" property="independentProject"/>
        <result column="submit_person" jdbcType="VARCHAR" property="submitPerson"/>
        <result column="submit_person_name" jdbcType="VARCHAR" property="submitPersonName"/>
        <result column="approval_person" jdbcType="VARCHAR" property="approvalPerson"/>
        <result column="approval_person_name" jdbcType="VARCHAR" property="approvalPersonName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="associated_id" jdbcType="BIGINT" property="associatedId"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="deleted" jdbcType="BIGINT" property="deleted"/>
        <result column="extension" jdbcType="VARCHAR" property="extension"/>
        <result column="yunshu_execute_unit_id" jdbcType="VARCHAR" property="yunshuExecuteUnitId"/>
        <result column="yunshu_execute_unit_code" jdbcType="VARCHAR" property="yunshuExecuteUnitCode"/>
        <result column="yunshu_execute_unit" jdbcType="VARCHAR" property="yunshuExecuteUnit"/>
        <result column="yunshu_execute_unit_id_path" jdbcType="VARCHAR" property="yunshuExecuteUnitIdPath"/>
        <result column="pre_file_id" jdbcType="BIGINT" property="preFileId"/>
        <result column="cpm_project_id" jdbcType="BIGINT" property="cpmProjectId"/>
        <result column="step_list" jdbcType="BIGINT" property="stepList"/>
        <result column="approval_type_id" jdbcType="BIGINT" property="approvalTypeId"/>
        <result column="current_step_no" jdbcType="BIGINT" property="currentStepNo"/>
        <result column="approval_begin_time" jdbcType="BIGINT" property="approvalBeginTime"/>
        <result column="dept_create_type" jdbcType="INTEGER" property="deptCreateType"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="parent_dept_id" jdbcType="VARCHAR" property="parentDeptId"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="dept_create_by" jdbcType="VARCHAR" property="deptCreateBy"/>
        <result column="step_version" jdbcType="INTEGER" property="stepVersion"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        t.id, t.belong_id, t.type, t.agreement_code, t.presentation_code, t.project_code, t.investment_code,
        t.project_name, t.customer_name, t.project_belong, t.province, t.city, t.region, t.country, t.address,
        t.country_project_type,t.market_project_type, t.market_project_type2, t.project_type, t.project_type2,
        t.project_type3, t.project_type4, t.is_create_head, t.is_independ_project, t.submit_person,
        t.submit_person_name, t.approval_person, t.approval_person_name, t.`status`, t.associated_id, t.create_by,
        t.create_at, t.update_by, t.update_at, t.deleted, t.extension, t.yunshu_execute_unit_id,
        t.yunshu_execute_unit_code, t.yunshu_execute_unit, t.yunshu_execute_unit_id_path, t.pre_file_id,
        t.cpm_project_id, t.step_list, t.approval_type_id, t.current_step_no, t.approval_begin_time,
        t.dept_create_type, t.dept_name, t.dept_abbr, t.parent_dept_id, t.dept_id, t.dept_create_by, t.step_version
    </sql>

    <select id="getOpenBidPage"
            resultType="com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response.BidApprovalPageResp">
        select
        t.id, t.project_name, t.yunshu_execute_unit as executeUnit, t.yunshu_execute_unit_id_path as executeUnitIdPath,
        t.customer_name, t.submit_person, t.submit_person_name, t.approval_person, t.approval_person_name, `type` as
        scopeType,
        t.is_create_head, t.is_independ_project as isIndependent, t.yunshu_execute_unit_id as executeUnitId,
        t.province,t.city, t.region,t.address, t.step_version,
        case t.type
        when 'tender_summary' then t.project_code
        when 'agreement' then t.agreement_code
        when 'no_contract_agreement' then t.agreement_code
        when 'internal_presentation' then t.presentation_code
        when 'internal_agreement' then t.agreement_code
        when 'no_contract_internal_agreement' then t.agreement_code
        when 'investment' then t.investment_code
        else t.project_code
        end as contractFileCode,
        t.create_at,
        p.cpm_project_key, p.cpm_project_name, p.create_at, pp.approve_finish_time as financeApprovalTime,
        pp.to_uc_time as
        smartSiteApprovalTime
        from bid_approval t
        inner join yunshu_org_sync yos on t.yunshu_execute_unit_id = yos.dept_id
        left join project p on t.cpm_project_id = p.id
        left join project_progress pp on p.id = pp.project_id
        where t.deleted = 0
        and yos.id_path like concat (#{queryIdPath}, '%')
        <if test="req.projectApprovalStatus != null and req.projectApprovalStatus.size() > 0">
            and (
            <foreach collection="req.projectApprovalStatus" item="status" index="index" separator="or">
                <choose>
                    <when test='status.equals("0") '>
                        t.is_independ_project = '0'
                    </when>
                    <when test='status.equals("D")'>
                        t.is_independ_project = 'D'
                    </when>
                    <when test='status.equals("Y0")'>
                        (t.is_independ_project = 'Y' and p.project_status = '0')
                    </when>
                    <when test='status.equals("N")'>
                        t.is_independ_project = 'N'
                    </when>
                    <when test='status.equals("Y1")'>
                        (t.is_independ_project = 'Y' and p.project_status = '1')
                    </when>
                </choose>
            </foreach>
            )
        </if>
        <if test="req.projectName != null and req.projectName != ''">
            and t.project_name like concat('%', #{req.projectName}, '%')
        </if>
        <if test="req.customerName != null and req.customerName != ''">
            and t.customer_name like concat('%', #{req.customerName}, '%')
        </if>
        <if test="req.createAtStart != null and req.createAtEnd != null">
            and t.create_at between #{req.createAtStart} and #{req.createAtEnd}
        </if>
        <if test="req.createHead != null and req.createHead != ''">
            and t.is_create_head = #{req.createHead}
        </if>
        <if test="req.province != null and req.province != ''">
            and t.province like concat('%', #{req.province},'%')
        </if>
        <if test="req.city != null and req.city != ''">
            and t.city like concat('%', #{req.city},'%')
        </if>
        <if test="req.scopeType != null and req.scopeType.size() > 0">
            and t.type in
            <foreach collection="req.scopeType" item="type" index="index" open="(" close=")" separator=",">
                #{type}
            </foreach>
        </if>
        <choose>
            <when test="req.orderBy == 'desc'">
                order by t.create_at desc
            </when>
            <otherwise>
                order by t.create_at
            </otherwise>
        </choose>
    </select>

    <select id="getBidPage"
            resultType="com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response.BidApprovalPageResp">
        select
        ba.id, ba.belong_id, ba.project_name, ba.yunshu_execute_unit as executeUnit, ba.yunshu_execute_unit_id_path as
        executeUnitIdPath,
        ba.customer_name, ba.submit_person, ba.submit_person_name, ba.approval_person, ba.approval_person_name, ba.type
        as scopeType,
        ba.is_create_head, ba.is_independ_project as isIndependent,
        ba.province, ba.city, ba.region, ba.address,
        case ba.type
        when 'tender_summary' then ba.project_code
        when 'agreement' then ba.agreement_code
        when 'no_contract_agreement' then ba.agreement_code
        when 'internal_presentation' then ba.presentation_code
        when 'internal_agreement' then ba.agreement_code
        when 'no_contract_internal_agreement' then ba.agreement_code
        when 'investment' then ba.investment_code
        else ba.project_code
        end as contractFileCode,
        ba.type as scopeType,
        ba.status as status,
        ba.approval_type_id as approvalTypeId,
        ba.create_at, ba.update_at,
        at.type_name as approvalTypeName,
        at.type_code as approvalTypeCode,
        ba.step_list,
        wa.proc_def_id,
        wa.proc_inst_id,
        ba.current_step_no,
        p.cpm_project_key
        from bid_approval ba
        inner join yunshu_org_sync yos on ba.yunshu_execute_unit_id = yos.dept_id
        left join approval_type at on ba.approval_type_id = at.type_id and at.deleted = 0
        left join wf_approval wa on cast(ba.id as char) = wa.belong_id and at.deleted = 0
        left join project p on ba.cpm_project_id = p.id
        where ba.deleted = 0
        and yos.id_path like concat(#{r.executeUnitIdPath}, '%')
        <if test="ids != null and ids.size() != 0">
            and ba.approval_type_id in
            <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="r.contractFileCode != null and r.contractFileCode != ''">
            and (ba.project_code like concat('%', #{r.contractFileCode}, '%')
            or ba.agreement_code like concat('%', #{r.contractFileCode}, '%')
            or ba.presentation_code like concat('%', #{r.contractFileCode}, '%')
            or ba.investment_code like concat('%', #{r.contractFileCode}, '%'))
        </if>
        <if test="r.projectName != null and r.projectName != ''">
            and ba.project_name like concat('%', #{r.projectName}, '%')
        </if>
        <if test="r.customerName != null and r.customerName != ''">
            and ba.customer_name like concat('%', #{r.customerName}, '%')
        </if>
        <if test="r.createAtStart != null and r.createAtEnd != null">
            and ba.create_at between #{r.createAtStart} and #{r.createAtEnd}
        </if>
        <if test="r.createHead != null and r.createHead != ''">
            and ba.is_create_head = #{r.createHead}
        </if>
        <if test="r.province != null and r.province != ''">
            and ba.province like concat('%', #{r.province},'%')
        </if>
        <if test="r.city != null and r.city != ''">
            and ba.city like concat('%', #{r.city},'%')
        </if>
        <if test="r.region != null and r.region != ''">
            and ba.region like concat('%', #{r.region},'%')
        </if>
        <if test="r.scopeType != null and r.scopeType != ''">
            and ba.type = #{r.scopeType}
        </if>
        order by ba.create_at desc
    </select>

    <select id="independentProjectByBelongId"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.PotentialRiskOfProjectInfoResp">
        select t.belong_id contractBelongId,
        t.type scopeType,
        case t.type
        when 'tender_summary' then t.project_code
        when 'agreement' then t.agreement_code
        when 'no_contract_agreement' then t.agreement_code
        when 'internal_presentation' then t.presentation_code
        when 'internal_agreement' then t.agreement_code
        when 'no_contract_internal_agreement' then t.agreement_code
        when 'investment' then t.investment_code
        else t.project_code
        end as contractFileCode,
        p.id cpmProjectId, p.cpm_project_name cpmProjectName, p.cpm_project_key cpmProjectKey, p.project_finance_code
        from bid_approval t
        left join project p on t.belong_id = p.id
        where t.deleted = 0
        and t.is_independ_project = 'Y'
        and t.belong_id in
        <foreach collection="belongIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="businessSystemDataPushInfo"
            resultType="com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.BusinessSystemDataPushInfoResp">
        select pepr.project_id as cpm_project_id,
        pepr.push_system_id,
        max(pepr.push_time) push_time,
        pepr.is_push_success,
        pepr.project_msg_id,
        pepr.err_msg,
        pes.id,
        pes.subscriber,
        pes.yunshu_execute_unit_id,
        pes.yunshu_execute_unit_id_path,
        yos.org_type
        from project_event_push_record pepr
        inner join project_event_subscribe pes on
        pepr.push_system_id = pes.id
        inner join yunshu_org_sync yos on
        pes.yunshu_execute_unit_id = yos.dept_id
        where pepr.project_id = #{cpmProjectId}
        group by pepr.push_system_id
    </select>

    <select id="getByBidId"
            resultType="com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response.BidApprovalDetailResp">
        select p.cpm_project_key, p.cpm_project_name,
        wa.proc_inst_id, wa.create_by as workflowStarter, wa.bill_state,
        <include refid="Base_Column_List"/>,
        t.is_independ_project as independentProject,
        t.is_engineering_project as engineeringProject
        from bid_approval t
        left join project p on t.cpm_project_id = p.id
        left join wf_approval wa on cast(t.id as char) = wa.belong_id
        where t.deleted = 0 and t.id = #{bidId}
    </select>
</mapper>