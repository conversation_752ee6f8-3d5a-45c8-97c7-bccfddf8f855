<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.BureauSupplementaryAgreementMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.BureauSupplementaryAgreement">
        <!--@mbg.generated-->
        <!--@Table bureau_supplementary_agreement-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="submit_person" jdbcType="VARCHAR" property="submitPerson"/>
        <result column="agreement_code" jdbcType="VARCHAR" property="agreementCode"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="project_belong" jdbcType="VARCHAR" property="projectBelong"/>
        <result column="bureau_project" jdbcType="VARCHAR" property="bureauProject"/>
        <result column="mandate_foreign" jdbcType="VARCHAR" property="mandateForeign"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="superior_company_name" jdbcType="VARCHAR" property="superiorCompanyName"/>
        <result column="enterprise_type" jdbcType="VARCHAR" property="enterpriseType"/>
        <result column="contact_person" jdbcType="VARCHAR" property="contactPerson"/>
        <result column="contract_responsible_person" jdbcType="VARCHAR" property="contractResponsiblePerson"/>
        <result column="company_assessment_indicators" jdbcType="VARCHAR" property="companyAssessmentIndicators"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="supplement_amount" jdbcType="DECIMAL" property="supplementAmount"/>
        <result column="pricing_method" jdbcType="VARCHAR" property="pricingMethod"/>
        <result column="contract_form" jdbcType="VARCHAR" property="contractForm"/>
        <result column="cost_of_labor_change" jdbcType="VARCHAR" property="costOfLaborChange"/>
        <result column="cost_of_labor_change2" jdbcType="VARCHAR" property="costOfLaborChange2"/>
        <result column="advances_flag" jdbcType="VARCHAR" property="advancesFlag"/>
        <result column="advances_way" jdbcType="VARCHAR" property="advancesWay"/>
        <result column="advances_month_rate" jdbcType="VARCHAR" property="advancesMonthRate"/>
        <result column="completed_rate" jdbcType="VARCHAR" property="completedRate"/>
        <result column="completed_cycle" jdbcType="VARCHAR" property="completedCycle"/>
        <result column="settlement_rate" jdbcType="VARCHAR" property="settlementRate"/>
        <result column="settlement_cycle" jdbcType="VARCHAR" property="settlementCycle"/>
        <result column="warranty_premium" jdbcType="VARCHAR" property="warrantyPremium"/>
        <result column="warranty_premium_rate" jdbcType="VARCHAR" property="warrantyPremiumRate"/>
        <result column="warranty_premium_way" jdbcType="VARCHAR" property="warrantyPremiumWay"/>
        <result column="pay_type_new" jdbcType="VARCHAR" property="payTypeNew"/>
        <result column="specific_pay_way" jdbcType="VARCHAR" property="specificPayWay"/>
        <result column="advances_fund_flag" jdbcType="VARCHAR" property="advancesFundFlag"/>
        <result column="guarantee_way" jdbcType="VARCHAR" property="guaranteeWay"/>
        <result column="agreement_url" jdbcType="LONGVARCHAR" property="agreementUrl"/>
        <result column="contract_term_url" jdbcType="LONGVARCHAR" property="contractTermUrl"/>
        <result column="law_url" jdbcType="LONGVARCHAR" property="lawUrl"/>
        <result column="bureau_supplementary_agreement_code" jdbcType="VARCHAR"
                property="bureauSupplementaryAgreementCode"/>
        <result column="independent_contract_id" jdbcType="BIGINT" property="independentContractId"/>
        <result column="independent_contract_type" jdbcType="INTEGER" property="independentContractType"/>
        <result column="belong_id" jdbcType="BIGINT" property="belongId"/>
        <result column="customer_level" jdbcType="VARCHAR" property="customerLevel"/>
        <result column="contact_person_mobile" jdbcType="VARCHAR" property="contactPersonMobile"/>
        <result column="break_bottom" jdbcType="LONGVARCHAR" property="breakBottom"/>
        <result column="belong_file_type" jdbcType="TINYINT" property="belongFileType"/>
        <result column="yunshu_execute_unit" jdbcType="VARCHAR" property="yunshuExecuteUnit"/>
        <result column="yunshu_execute_unit_code" jdbcType="VARCHAR" property="yunshuExecuteUnitCode"/>
        <result column="yunshu_execute_unit_id" jdbcType="VARCHAR" property="yunshuExecuteUnitId"/>
        <result column="yunshu_execute_unit_id_path" jdbcType="VARCHAR" property="yunshuExecuteUnitIdPath"/>
        <result column="pre_file_type" jdbcType="TINYINT" property="preFileType"/>
        <result column="pre_file_id" jdbcType="BIGINT" property="preFileId"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="source" jdbcType="TINYINT" property="source"/>
        <result column="is_independent" jdbcType="VARCHAR" property="independent"/>
        <result column="signed_subject_value" jdbcType="VARCHAR" property="signedSubjectValue"/>
        <result column="signed_subject_code" jdbcType="VARCHAR" property="signedSubjectCode"/>
        <result column="business_type_code" jdbcType="VARCHAR" property="businessTypeCode"/>
        <result column="customer_level_code" jdbcType="VARCHAR" property="customerLevelCode"/>
        <result column="enterprise_type_code" jdbcType="VARCHAR" property="enterpriseTypeCode"/>
        <result column="customer_id" jdbcType="VARCHAR" property="customerId"/>
        <result column="advances_way_code" jdbcType="VARCHAR" property="advancesWayCode"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="superior_company_id" jdbcType="VARCHAR" property="superiorCompanyId"/>
        <result column="business_license_code" jdbcType="VARCHAR" property="businessLicenseCode"/>
        <result column="business_segment_code_path" jdbcType="VARCHAR" property="businessSegmentCodePath"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, submit_person, agreement_code, project_name, project_belong, bureau_project,
        mandate_foreign, customer_name, superior_company_name, enterprise_type, contact_person,
        contract_responsible_person, company_assessment_indicators, business_type, supplement_amount,
        pricing_method, contract_form, cost_of_labor_change, cost_of_labor_change2, advances_flag,
        advances_way, advances_month_rate, completed_rate, completed_cycle, settlement_rate,
        settlement_cycle, warranty_premium, warranty_premium_rate, warranty_premium_way,
        pay_type_new, specific_pay_way, advances_fund_flag, guarantee_way, agreement_url,
        contract_term_url, law_url, bureau_supplementary_agreement_code, independent_contract_id,
        independent_contract_type, belong_id, customer_level, contact_person_mobile, break_bottom,
        belong_file_type, yunshu_execute_unit, yunshu_execute_unit_code, yunshu_execute_unit_id,
        yunshu_execute_unit_id_path, pre_file_type, pre_file_id, create_at, update_at, `source`,
        is_independent, signed_subject_value, signed_subject_code, business_type_code, customer_level_code,
        enterprise_type_code, customer_id, advances_way_code, customer_code, superior_company_id,
        business_license_code, business_segment_code_path
    </sql>
    <insert id="saveBureauSupplementaryAgreement"
            parameterType="com.cscec3b.iti.projectmanagement.server.entity.BureauSupplementaryAgreement"
            useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO bureau_supplementary_agreement(`submit_person`,
                                                   `agreement_code`,
                                                   `project_name`,
                                                   `project_belong`,
                                                   `bureau_project`,
                                                   `mandate_foreign`,
                                                   `customer_name`,
                                                   `superior_company_name`,
                                                   `enterprise_type`,
                                                   `contact_person`,
                                                   `contact_person_mobile`,
                                                   `contract_responsible_person`,
                                                   `company_assessment_indicators`,
                                                   `business_type`,
                                                   `supplement_amount`,
                                                   `pricing_method`,
                                                   `contract_form`,
                                                   `cost_of_labor_change`,
                                                   `cost_of_labor_change2`,
                                                   `advances_flag`,
                                                   `advances_way`,
                                                   `advances_month_rate`,
                                                   `completed_rate`,
                                                   `completed_cycle`,
                                                   `settlement_rate`,
                                                   `settlement_cycle`,
                                                   `warranty_premium`,
                                                   `warranty_premium_rate`,
                                                   `warranty_premium_way`,
                                                   `pay_type_new`,
                                                   `specific_pay_way`,
                                                   `advances_fund_flag`,
                                                   `guarantee_way`,
                                                   `agreement_url`,
                                                   `contract_term_url`,
                                                   `law_url`,
                                                   `bureau_supplementary_agreement_code`,
                                                   `independent_contract_id`,
                                                   `independent_contract_type`,
                                                   `belong_id`,
                                                   `customer_level`,
        `signed_subject_value`,
        `signed_subject_code`,
        `break_bottom`,
        `create_at`,
        `update_at`
        )
        VALUES (#{vo.submitPerson},
                #{vo.agreementCode},
                #{vo.projectName},
                #{vo.projectBelong},
                #{vo.bureauProject},
                #{vo.mandateForeign},
                #{vo.customerName},
                #{vo.superiorCompanyName},
                #{vo.enterpriseType},
                #{vo.contactPerson},
                #{vo.contactPersonMobile},
                #{vo.contractResponsiblePerson},
                #{vo.companyAssessmentIndicators},
                #{vo.businessType},
                #{vo.supplementAmount},
                #{vo.pricingMethod},
                #{vo.contractForm},
                #{vo.costOfLaborChange},
                #{vo.costOfLaborChange2},
                #{vo.advancesFlag},
                #{vo.advancesWay},
                #{vo.advancesMonthRate},
                #{vo.completedRate},
                #{vo.completedCycle},
                #{vo.settlementRate},
                #{vo.settlementCycle},
                #{vo.warrantyPremium},
                #{vo.warrantyPremiumRate},
                #{vo.warrantyPremiumWay},
                #{vo.payTypeNew},
                #{vo.specificPayWay},
                #{vo.advancesFundFlag},
                #{vo.guaranteeWay},
                #{vo.agreementUrl},
                #{vo.contractTermUrl},
                #{vo.lawUrl},
                #{vo.bureauSupplementaryAgreementCode},
                #{vo.independentContractId},
                #{vo.independentContractType},
                #{vo.belongId},
                #{vo.customerLevel},
        #{vo.signedSubjectValue},
        #{vo.signedSubjectCode},
        #{vo.breakBottom},
        #{vo.createAt},
        #{vo.updateAt})
    </insert>
    <select id="selectCountByBelongId" resultType="int">
        SELECT COUNT(*)
        FROM bureau_supplementary_agreement
        WHERE belong_id = #{belongId}
    </select>
    <select id="getById"
            resultType="com.cscec3b.iti.projectmanagement.server.entity.BureauSupplementaryAgreement">
        SELECT `id`,
               `submit_person`,
               `agreement_code`,
               `project_name`,
               `project_belong`,
               `bureau_project`,
               `mandate_foreign`,
               `customer_name`,
               `superior_company_name`,
               `enterprise_type`,
               `contact_person`,
               `contact_person_mobile`,
               `contract_responsible_person`,
               `company_assessment_indicators`,
               `business_type`,
               `supplement_amount`,
               `pricing_method`,
               `contract_form`,
               `cost_of_labor_change`,
               `cost_of_labor_change2`,
               `advances_flag`,
               `advances_way`,
        `advances_way_code`,
               `advances_month_rate`,
               `completed_rate`,
               `completed_cycle`,
               `settlement_rate`,
               `settlement_cycle`,
               `warranty_premium`,
               `warranty_premium_rate`,
               `warranty_premium_way`,
               `pay_type_new`,
               `specific_pay_way`,
               `advances_fund_flag`,
               `guarantee_way`,
               `agreement_url`,
               `contract_term_url`,
        `law_url`,
        `bureau_supplementary_agreement_code`,
        `independent_contract_id`,
        `independent_contract_type`,
        `belong_id`,
        `customer_level`,
        `break_bottom`,
        `signed_subject_value`,
        `signed_subject_code`
        FROM bureau_supplementary_agreement
        WHERE id = #{id}
    </select>

    <delete id="deleteBureauSupplementaryAgreementByBelongIds">
        delete from bureau_supplementary_agreement
        where belong_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="pageList" resultType="com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp">
        select t.id, t.belong_id ,
        #{scopeTypeEnum.dictCode} as scopeType, #{scopeTypeEnum.zhCN} as scopeTypeName,
        t.project_name as projectName,
        t.agreement_code as contractFileCode,
        t.pre_file_id,
        t.pre_file_type,
        <if test="scopeTypeEnum.dictCode == 5">
            t2.project_name as preProjectName,
            t2.belong_id as preFileId,
            t2.belong_id preFileBelongId,
            t2.id as preFileKeyId,
        </if>
        t1.id as cpmProjectId,
        t1.cpm_project_key,
        t1.cpm_project_name,
        t1.cpm_project_abbreviation,
        t1.source_system,
        t.create_at,
        t.update_at
        from bureau_supplementary_agreement t
        left join project t1 on t.independent_contract_id = t1.independent_contract_id
        <!-- -->
        <if test="scopeTypeEnum.dictCode == 5">
            left join bureau_contract t2 on t.pre_file_id is not null and t.pre_file_id = t2.belong_id
        </if>
        <where>
            t.belong_file_type= #{scopeTypeEnum.dictCode}
            <if test="req.projectName != null and req.projectName != ''">
                and t.project_name like concat('%', #{req.projectName}, '%')
            </if>
            <if test="req.contractFileCode != null and req.contractFileCode != ''">
                and t.bureau_supplementary_agreement_code like concat('%', #{req.contractFileCode}, '%')
            </if>
            <if test="req.yunshueExecuteUnitQueryCode != null and req.yunshueExecuteUnitQueryCode != ''">
                and t.yunshu_execute_unit_id_path like concat('%', #{req.yunshueExecuteUnitQueryCode}, '%')
            </if>
            <if test="req.relativeProject != null and req.relativeProject == 1">
                and t1.id is not null
                <if test="req.cpmProjectKey != null and req.cpmProjectKey != ''">
                    and t1.cpm_project_key like concat('%', #{req.cpmProjectKey}, '%')
                </if>
                <if test="req.cpmProjectName != null and req.cpmProjectName != ''">
                    and (t1. project_name like concat('%', #{req.cpmProjectName}, '%')
                    or t1.project_finance_name like concat('%', #{req.cpmProjectName}, '%'))
                </if>
            </if>
            <if test="req.relativeProject != null and req.relativeProject == 0">
                and t1.id is null
            </if>
        </where>
        order by t.create_at desc
    </select>
</mapper>

