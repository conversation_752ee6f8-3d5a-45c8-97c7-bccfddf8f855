package com.cscec3b.iti.retry.aop;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.RetryContext;
import org.springframework.retry.backoff.ExponentialBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.retry.annotations.PmReTry;
import com.cscec3b.iti.retry.config.RetryContextHolder;
import com.cscec3b.iti.retry.model.dto.CallBackDto;
import com.cscec3b.iti.retry.server.ApiRetryCallService;

/**
 * <AUTHOR> <br/>
 * &#064;Date 2022/11/09 10:11
 **/
@Aspect
@Component
public class RetryRecoverAspect {

    private static final Logger log = LoggerFactory.getLogger(RetryRecoverAspect.class);
    private final RetryTemplate retryTemplate;

    private final ApiRetryCallService callService;
    
    public RetryRecoverAspect(RetryTemplate retryTemplate, ApiRetryCallService callService) {
        this.retryTemplate = retryTemplate;
        this.callService = callService;
    }

    /**
     * 重试切入点
     */
    @Pointcut("@annotation(com.cscec3b.iti.retry.annotations.PmReTry)")
    public void retryPointcut() {
        // doNothing
    }

    /**
     * around
     *
     * @param point 点
     * @return {@link Object}
     * @throws Throwable throwable
     */
    @Around("retryPointcut()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        final MethodSignature signature = (MethodSignature) point.getSignature();
        PmReTry reTry = signature.getMethod().getAnnotation(PmReTry.class);
        // 配置策略
        setRetryTemplateConfig(reTry, retryTemplate);
        
        return retryTemplate.execute(context -> {
            // 业务方法
            final int retryCount = context.getRetryCount() + 1;
            
            log.warn("Thread: {} -Retry for the {} time", Thread.currentThread().getName(), retryCount);
                return point.proceed();
        }, recoveryCallback -> {
            // 兜底方案
            handleRecovery(point, signature, reTry, recoveryCallback);
            throw new FrameworkException(-1,recoveryCallback.getLastThrowable().getMessage());
//            throw new RetryException(recoveryCallback.getLastThrowable().getMessage());
        });
        
    }
    
    /**
     * 配置策略
     *
     * @param reTry retry 注解
     * @param retryTemplate retryTemplate
     */
    private static void setRetryTemplateConfig(PmReTry reTry, RetryTemplate retryTemplate) {
        ExponentialBackOffPolicy backOffPolicy = new ExponentialBackOffPolicy();
        // 初始等待时间
        backOffPolicy.setInitialInterval(reTry.intervalTime());
        // 时间等待倍数
        backOffPolicy.setMultiplier(reTry.multiplier());
        // 最大等待时间
        backOffPolicy.setMaxInterval(reTry.maxInterval());
        retryTemplate.setBackOffPolicy(backOffPolicy);
        // 配置策略
        retryTemplate.setRetryPolicy(new SimpleRetryPolicy(reTry.maxAttempts()));
    }
    
    private void handleRecovery(ProceedingJoinPoint point, MethodSignature signature, PmReTry reTry,
            RetryContext recoveryCallback) {
        final String className = signature.getDeclaringTypeName();
        final String methodName = signature.getName();
        // retryId 是从定时任务轮询重试方法设置来的，从请求或定时任务中不会设置retryId
        String retryId = RetryContextHolder.retryId();
        log.info("retry id is : {}", retryId);
        
        if (StringUtils.isBlank(retryId) && reTry.isRecover()) {
            log.error("recovery");
            recover(point, signature, recoveryCallback.getLastThrowable(), className, methodName);
        }
        
        log.error("Scheduled task retry failed for {}.{} after multiple retries", className, methodName);
    }
    
    
    /**
     * 兜底方案
     *
     * @param point         切入点
     * @param signature     方法签名
     * @param lastThrowable 异常信息
     * @param className     类名
     * @param methodName    方法名
     */
    private void recover(ProceedingJoinPoint point, MethodSignature signature, Throwable lastThrowable, String className, String methodName) {
        log.error("recovery");
        // 执行recover方法
        Object[] args = point.getArgs();
        String[] params = new String[args.length];
        for (int i = 0; i < args.length; i++) {
            params[i] = JsonUtils.toJsonStr(args[i]);
        }
        
        Class[] parameterTypes = signature.getParameterTypes();
        String result = null != lastThrowable ? lastThrowable.toString() : null;
        CallBackDto backDto = new CallBackDto().setClassName(className).setMethodName(methodName)
                                      .setParamTypes(parameterTypes).setParams(params).setResult(result);
        callService.setCallBackData(backDto);
    }
}
